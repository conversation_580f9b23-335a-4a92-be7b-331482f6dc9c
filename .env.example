# AI-Relief 前端环境变量配置示例
# 复制此文件为 .env.local 并根据实际情况修改配置

# ===== 基础配置 =====
# API基础URL - 后端服务地址
VITE_API_BASE_URL=http://localhost:9999

# 是否启用Mock模式 (true/false)
# 如果不设置且没有API_BASE_URL，会自动启用Mock
VITE_ENABLE_MOCK=false

# ===== 外部AI服务配置 =====
# 外部AI服务URL
VITE_AI_SERVICE_URL=https://www.airelief.cn:8997/agent

# 外部AI服务API密钥
VITE_AI_API_KEY=test_api_key

# ===== 语音识别配置 =====
# 火山引擎语音识别配置（如果后端使用）
# 这些配置主要在后端使用，前端可以不设置
# VITE_VOLCENGINE_APP_ID=your_app_id
# VITE_VOLCENGINE_ACCESS_KEY=your_access_key
# VITE_VOLCENGINE_SECRET_KEY=your_secret_key

# ===== 微信配置 =====
# 微信公众号配置（如果需要）
# VITE_WECHAT_APP_ID=your_wechat_app_id

# ===== 开发配置 =====
# 开发模式下的调试选项
VITE_DEBUG=true

# ===== 生产环境配置示例 =====
# 生产环境下的配置示例（注释掉的配置）
# VITE_API_BASE_URL=https://api.airelief.cn
# VITE_ENABLE_MOCK=false
# VITE_AI_SERVICE_URL=https://www.airelief.cn:8997/agent
# VITE_AI_API_KEY=your_production_api_key
# VITE_DEBUG=false
